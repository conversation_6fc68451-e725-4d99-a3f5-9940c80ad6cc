#!/bin/bash

# 폐쇄망 설치를 위한 이미지 준비 스크립트
echo "=== 폐쇄망 설치용 이미지 준비 스크립트 ==="

# 스크립트 디렉토리 확인
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "스크립트 디렉토리: $SCRIPT_DIR"
echo "프로젝트 루트: $PROJECT_ROOT"

# package.json 파일 확인
if [ ! -f "$PROJECT_ROOT/package.json" ]; then
    echo "❌ package.json 파일을 찾을 수 없습니다."
    echo "현재 위치: $(pwd)"
    echo "프로젝트 루트: $PROJECT_ROOT"
    echo "사용법: cd /path/to/lx-chatbot && ./install/prepare-offline.sh"
    exit 1
fi

echo "✓ 프로젝트 루트 확인 완료: $PROJECT_ROOT"

# 1. 애플리케이션 빌드 (프로젝트 루트에서)
echo "1. 애플리케이션 빌드 중..."
cd "$PROJECT_ROOT"
docker build -t lx-chatbot/frontend:latest -f Dockerfile .
if [ $? -ne 0 ]; then
    echo "❌ 빌드 실패"
    exit 1
fi
cd "$SCRIPT_DIR"

# 2. 이미지 저장
echo "2. Docker 이미지 저장 중..."

# 메인 애플리케이션 이미지 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
echo "✓ lx-chatbot-frontend.tar 저장 완료"

# 베이스 이미지 저장
docker save node:20-alpine -o node-20-alpine.tar
echo "✓ node-20-alpine.tar 저장 완료"

# 3. 파일 크기 확인
echo "3. 저장된 파일 정보:"
ls -lh *.tar

# 4. 필요한 파일들 복사
echo "4. 설치 파일들 준비 중..."
chmod +x install-offline.sh

# 5. 압축 (선택사항)
echo "5. 파일 압축 중..."

# 압축할 파일 목록 생성 (존재하는 파일만)
FILES_TO_COMPRESS=""
for file in *.tar; do
    if [ -f "$file" ]; then
        FILES_TO_COMPRESS="$FILES_TO_COMPRESS $file"
    fi
done

# 프로젝트 루트의 파일들 확인
if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
    cp "$PROJECT_ROOT/docker-compose.yml" .
    FILES_TO_COMPRESS="$FILES_TO_COMPRESS docker-compose.yml"
fi

# install 디렉토리의 파일들 확인
if [ -f "install-offline.sh" ]; then
    FILES_TO_COMPRESS="$FILES_TO_COMPRESS install-offline.sh"
fi

# .env.example 파일 생성
cat > .env.example << 'EOF'
# LX Chatbot 환경변수 설정 예제
# 실제 사용 시 이 파일을 .env로 복사하고 값을 수정하세요

# Dify 설정 (AI 모델 서비스)
DIFY_APP_KEY=your_dify_app_key_here
DIFY_URL=http://your-dify-server:port

# vLLM 설정 (로컬 LLM 서버)
VLLM_BASE_URL=http://your-vllm-server:port/v1
VLLM_API_KEY=your_vllm_api_key_here
EOF
FILES_TO_COMPRESS="$FILES_TO_COMPRESS .env.example"

# Windows 설치 스크립트 생성
cat > install-offline.bat << 'EOF'
@echo off
chcp 65001 > nul
echo === LX Chatbot 폐쇄망 설치 스크립트 (Windows) ===

REM Docker 설치 확인
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker가 설치되어 있지 않습니다.
    pause
    exit /b 1
)

echo ✓ Docker 확인 완료

REM 이미지 로드
echo 2. Docker 이미지 로드 중...
if exist "lx-chatbot-frontend.tar" (
    docker load -i lx-chatbot-frontend.tar
    echo ✓ lx-chatbot-frontend 이미지 로드 완료
) else (
    echo ❌ lx-chatbot-frontend.tar 파일을 찾을 수 없습니다.
    pause
    exit /b 1
)

if exist "node-20-alpine.tar" (
    docker load -i node-20-alpine.tar
    echo ✓ node-20-alpine 이미지 로드 완료
)

REM 환경변수 파일 확인
if not exist ".env" (
    if exist ".env.example" (
        copy .env.example .env
        echo ✓ .env.example을 .env로 복사했습니다.
        echo ⚠️  .env 파일을 편집하여 환경변수를 설정해주세요.
        pause
    )
)

REM 컨테이너 실행
echo 4. 컨테이너 실행 중...
docker-compose up -d

echo.
echo === 설치 완료 ===
echo LX Chatbot이 http://localhost:3002 에서 실행 중입니다.
pause
EOF
FILES_TO_COMPRESS="$FILES_TO_COMPRESS install-offline.bat"

if [ -n "$FILES_TO_COMPRESS" ]; then
    tar -czf lx-chatbot-offline-package.tar.gz $FILES_TO_COMPRESS
    echo "✓ lx-chatbot-offline-package.tar.gz 생성 완료"
else
    echo "❌ 압축할 파일이 없습니다."
fi

echo ""
echo "=== 준비 완료 ==="
echo "폐쇄망으로 복사할 파일들:"
echo "- lx-chatbot-offline-package.tar.gz (전체 패키지)"
echo "또는 개별 파일들:"
echo "- lx-chatbot-frontend.tar"
echo "- node-20-alpine.tar"
echo "- docker-compose.yml"
echo "- install-offline.sh (Linux용)"
echo "- install-offline.bat (Windows용)"
echo "- .env (환경변수 설정)"
